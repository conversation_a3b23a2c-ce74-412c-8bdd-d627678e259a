Option Explicit

' <PERSON><PERSON> Macro to Send All Emails in Draft Folder
' Author: Augment Agent
' Description: This macro sends all emails currently in the Drafts folder
' Usage: Run this macro from Outlook VBA Editor (Alt+F11)

Sub SendAllDrafts()
    Dim olApp As Outlook.Application
    Dim olNamespace As Outlook.Namespace
    Dim olDraftsFolder As Outlook.MAPIFolder
    Dim olMailItem As Outlook.MailItem
    Dim i As Integer
    Dim totalDrafts As Integer
    Dim sentCount As Integer
    Dim errorCount As Integer
    Dim errorMessages As String
    
    ' Initialize variables
    sentCount = 0
    errorCount = 0
    errorMessages = ""
    
    On Error GoTo ErrorHandler
    
    ' Get Outlook application
    Set olApp = Application
    Set olNamespace = olApp.GetNamespace("MAPI")
    
    ' Get the Drafts folder
    Set olDraftsFolder = olNamespace.GetDefaultFolder(olFolderDrafts)
    
    ' Check if there are any drafts
    totalDrafts = olDraftsFolder.Items.Count
    
    If totalDrafts = 0 Then
        MsgBox "No draft emails found to send.", vbInformation, "Send All Drafts"
        GoTo CleanUp
    End If
    
    ' Confirm with user before sending
    Dim response As VbMsgBoxResult
    response = MsgBox("Found " & totalDrafts & " draft email(s). Do you want to send all of them?", _
                      vbYesNo + vbQuestion, "Confirm Send All Drafts")
    
    If response = vbNo Then
        MsgBox "Operation cancelled by user.", vbInformation, "Send All Drafts"
        GoTo CleanUp
    End If
    
    ' Loop through drafts folder (backwards to avoid index issues)
    For i = totalDrafts To 1 Step -1
        On Error Resume Next
        
        ' Check if the item is a mail item
        If olDraftsFolder.Items(i).Class = olMail Then
            Set olMailItem = olDraftsFolder.Items(i)
            
            ' Validate email before sending
            If ValidateEmail(olMailItem) Then
                ' Send the email
                olMailItem.Send
                
                ' Check for errors
                If Err.Number = 0 Then
                    sentCount = sentCount + 1
                    Debug.Print "Sent: " & olMailItem.Subject
                Else
                    errorCount = errorCount + 1
                    errorMessages = errorMessages & "Error sending '" & olMailItem.Subject & "': " & Err.Description & vbCrLf
                    Err.Clear
                End If
            Else
                errorCount = errorCount + 1
                errorMessages = errorMessages & "Validation failed for '" & olMailItem.Subject & "': Missing recipient or subject" & vbCrLf
            End If
        End If
        
        On Error GoTo ErrorHandler
    Next i
    
    ' Show results
    Dim resultMessage As String
    resultMessage = "Operation completed!" & vbCrLf & vbCrLf & _
                   "Emails sent: " & sentCount & vbCrLf & _
                   "Errors: " & errorCount
    
    If errorCount > 0 Then
        resultMessage = resultMessage & vbCrLf & vbCrLf & "Error details:" & vbCrLf & errorMessages
        MsgBox resultMessage, vbExclamation, "Send All Drafts - Completed with Errors"
    Else
        MsgBox resultMessage, vbInformation, "Send All Drafts - Success"
    End If
    
    GoTo CleanUp
    
ErrorHandler:
    MsgBox "An unexpected error occurred: " & Err.Description & vbCrLf & _
           "Error Number: " & Err.Number, vbCritical, "Send All Drafts - Error"
    
CleanUp:
    ' Clean up objects
    Set olMailItem = Nothing
    Set olDraftsFolder = Nothing
    Set olNamespace = Nothing
    Set olApp = Nothing
End Sub

' Function to validate email before sending
Private Function ValidateEmail(mailItem As Outlook.MailItem) As Boolean
    ValidateEmail = False
    
    ' Check if email has recipients
    If mailItem.Recipients.Count = 0 Then
        Exit Function
    End If
    
    ' Check if email has a subject (optional but recommended)
    ' Uncomment the next 3 lines if you want to require a subject
    'If Trim(mailItem.Subject) = "" Then
    '    Exit Function
    'End If
    
    ValidateEmail = True
End Function

' Alternative macro to send drafts with user selection
Sub SendSelectedDrafts()
    Dim olApp As Outlook.Application
    Dim olNamespace As Outlook.Namespace
    Dim olDraftsFolder As Outlook.MAPIFolder
    Dim olMailItem As Outlook.MailItem
    Dim i As Integer
    Dim selectedItems As String
    Dim itemsToSend As Collection
    Dim sentCount As Integer
    
    Set itemsToSend = New Collection
    sentCount = 0
    
    On Error GoTo ErrorHandler
    
    ' Get Outlook application and drafts folder
    Set olApp = Application
    Set olNamespace = olApp.GetNamespace("MAPI")
    Set olDraftsFolder = olNamespace.GetDefaultFolder(olFolderDrafts)
    
    ' Check if there are any drafts
    If olDraftsFolder.Items.Count = 0 Then
        MsgBox "No draft emails found.", vbInformation, "Send Selected Drafts"
        GoTo CleanUp
    End If
    
    ' Build list of drafts for user selection
    selectedItems = "Select emails to send:" & vbCrLf & vbCrLf
    For i = 1 To olDraftsFolder.Items.Count
        If olDraftsFolder.Items(i).Class = olMail Then
            Set olMailItem = olDraftsFolder.Items(i)
            selectedItems = selectedItems & i & ". " & olMailItem.Subject & _
                           " (To: " & olMailItem.To & ")" & vbCrLf
        End If
    Next i
    
    ' For now, this is a placeholder - you could implement a custom form
    ' or use InputBox for simple selection
    MsgBox selectedItems & vbCrLf & "Use SendAllDrafts macro to send all drafts at once.", _
           vbInformation, "Draft List"
    
    GoTo CleanUp
    
ErrorHandler:
    MsgBox "An error occurred: " & Err.Description, vbCritical, "Send Selected Drafts - Error"
    
CleanUp:
    Set olMailItem = Nothing
    Set olDraftsFolder = Nothing
    Set olNamespace = Nothing
    Set olApp = Nothing
    Set itemsToSend = Nothing
End Sub
