# VBA Macro for Outlook - Send All Drafts

This repository contains a VBA macro for Microsoft Outlook that allows you to send all emails in your draft folder with a single click.

## Features

- **Send All Drafts**: Automatically sends all emails in the Drafts folder
- **Email Validation**: Validates emails before sending (checks for recipients)
- **Error Handling**: Comprehensive error handling with detailed error messages
- **User Confirmation**: Asks for confirmation before sending emails
- **Progress Reporting**: Shows how many emails were sent and any errors encountered
- **Safe Operation**: Validates each email before attempting to send

## Files

- `SendAllDrafts.bas` - The main VBA macro file containing the code
- `README.md` - This documentation file

## Installation Instructions

### Step 1: Enable Developer Tab in Outlook
1. Open Microsoft Outlook
2. Go to **File** → **Options**
3. Click **Customize Ribbon**
4. Check the **Developer** checkbox in the right panel
5. Click **OK**

### Step 2: Access VBA Editor
1. In Outlook, click the **Developer** tab
2. Click **Visual Basic** (or press Alt+F11)

### Step 3: Import the Macro
1. In the VBA Editor, right-click on **Project1** in the left panel
2. Select **Import File...**
3. Browse and select the `SendAllDrafts.bas` file
4. The macro will be imported into your Outlook VBA project

### Alternative: Copy and Paste Method
1. Open the VBA Editor (Alt+F11)
2. Right-click on **Project1** → **Insert** → **Module**
3. Open the `SendAllDrafts.bas` file in a text editor
4. Copy all the code and paste it into the new module
5. Save the project (Ctrl+S)

## Usage

### Method 1: Run from VBA Editor
1. Open VBA Editor (Alt+F11)
2. Press F5 or click **Run** → **Run Sub/UserForm**
3. Select `SendAllDrafts` from the list
4. Click **Run**

### Method 2: Create a Button (Recommended)
1. In Outlook, go to **File** → **Options** → **Customize Ribbon**
2. Create a new group or use an existing one
3. Add a **Macro** button
4. Assign the `SendAllDrafts` macro to the button
5. Click the button whenever you want to send all drafts

### Method 3: Quick Access Toolbar
1. Right-click on the Quick Access Toolbar
2. Select **Customize Quick Access Toolbar**
3. Choose **Macros** from the dropdown
4. Add the `SendAllDrafts` macro
5. Click **OK**

## How It Works

1. **Validation**: The macro first checks if there are any draft emails
2. **Confirmation**: Asks for user confirmation before proceeding
3. **Email Validation**: Each email is validated to ensure it has recipients
4. **Sending**: Emails are sent one by one with error handling
5. **Reporting**: Shows a summary of sent emails and any errors

## Safety Features

- **User Confirmation**: Always asks before sending emails
- **Email Validation**: Checks for recipients before sending
- **Error Handling**: Catches and reports errors without crashing
- **Detailed Logging**: Provides information about what was sent and any issues

## Troubleshooting

### Common Issues

1. **"Macro not found"**
   - Ensure the macro is properly imported
   - Check that macros are enabled in Outlook

2. **"Permission denied"**
   - Check Outlook security settings
   - Ensure macros are allowed to run

3. **"Object not found"**
   - Make sure Outlook is running
   - Verify you have a Drafts folder

### Security Settings
If macros don't run:
1. Go to **File** → **Options** → **Trust Center** → **Trust Center Settings**
2. Click **Macro Settings**
3. Select **Notifications for digitally signed macros, all other macros disabled** or **Enable all macros**

## Customization

You can modify the macro to:
- Require a subject line (uncomment lines in `ValidateEmail` function)
- Add delay between sends
- Filter drafts by specific criteria
- Add more detailed logging

## Important Notes

- **Test First**: Always test with a few draft emails before using on many emails
- **Backup**: Consider backing up your drafts before running the macro
- **Recipients**: Double-check your draft recipients before running
- **Undo**: Once sent, emails cannot be unsent - use with caution

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify your Outlook version supports VBA
3. Ensure all security settings allow macro execution

## Version History

- v1.0 - Initial release with basic send all drafts functionality
- Includes error handling and user confirmation
